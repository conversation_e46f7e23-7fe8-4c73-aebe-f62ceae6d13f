import Safe, { PredictedSafeProps } from '@safe-global/protocol-kit';
import * as Clipboard from 'expo-clipboard';
import { useCallback, useState, useTransition } from 'react';
import { FlatList, StyleSheet, View } from 'react-native';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Account, Chain, Hex, Transport, WaitForTransactionReceiptReturnType, WalletClient } from 'viem';
import { privateKeyToAddress } from 'viem/accounts';
import { sepolia } from 'viem/chains';
import { Icons } from '@/assets/icons';
import { CustomButton } from '@/components/Button';
import { Spacer } from '@/components/Spacer';
import { ThemedText } from '@/components/ThemedText';
import TextInput from '@/components/ui/TextInput';
import { useSafeContext } from '@/context/safeContext';
import { useCreateWallet } from '@/hooks/useCreateWallet';
import { useGetLocation } from '@/hooks/useGetLocation';
import { useGetRecoveryKey } from '@/hooks/useGetRecoveryKey';
import { useGetRecoveryKeysNewWallet } from '@/hooks/useGetRecoveryKeysNewWallet';
import { useSetupWallet } from '@/hooks/useSetupWallet';
import { useTheme } from '@/hooks/useThemeColor';

type Props = {};

export const SetupWallet = (props: Props) => {
  const { styles } = useStyles();
  const [signers, setSigners] = useState<string[]>([]);
  const [threshold, setThreshold] = useState<string>('');
  const [_isPending, startTransition] = useTransition();

  const { data: recoveryKey } = useGetRecoveryKey();
  const { data: recoveryKeys = [] } = useGetRecoveryKeysNewWallet();
  const { mutateAsync: createWallet, isPending: isCreatingWallet } = useCreateWallet();
  const { mutateAsync: setupWallet, isPending: isSettingUpWallet } = useSetupWallet();
  const { mutateAsync: getLocation, isPending: isFetchingLocation } = useGetLocation();
  const { onSetSigners, onSetThreshold, isLoading } = useSafeContext();

  const handleGenMultisig = useCallback(async () => {
    startTransition(async () => {
      try {
        const location = await getLocation();
        if (!location) return;

        const wallets = await Promise.all(recoveryKeys.map((recoveryKey) => createWallet({ recoveryKey, location })));
        const signersAddress = await Promise.all(
          wallets.map((wallet) => privateKeyToAddress(`0x${wallet?.privateKey}` as const))
        );

        setSigners(signersAddress);

        // await setupWallet({ privateKey, seedPhrase, recoveryKey });

        // navigation.dispatch(CommonActions.reset({ index: 0, routes: [{ name: '(app)(tabs)' }] }));

        // console.log(wallets);
      } catch (error) {
        console.error(error);
      }
    });
  }, [createWallet, getLocation, recoveryKeys]);

  const handleCopy = async (recoveryKey: string) => {
    if (!recoveryKey) return;

    await Clipboard.setStringAsync(recoveryKey);
  };

  const handleChangeThreshold = (text: string) => {
    if (text.length === 0) {
      setThreshold('');
    }

    if (text.match(/^\d+$/) && Number.parseInt(text) <= signers.length) {
      setThreshold(Number.parseInt(text).toString());
    }
  };

  async function deploySafe(protocolKit: Safe): Promise<WaitForTransactionReceiptReturnType<Chain>> {
    const safeDeploymentTransaction = await protocolKit.createSafeDeploymentTransaction();

    const signer = (await protocolKit.getSafeProvider().getExternalSigner()) as WalletClient<Transport, Chain, Account>;
    const client = protocolKit.getSafeProvider().getExternalProvider();

    if (!signer) throw new Error('SafeProvider must be initialized with a signer to use this function');

    const estGas = await client.estimateGas({
      to: safeDeploymentTransaction.to as `0x${string}`,
      data: safeDeploymentTransaction.data as Hex,
      value: BigInt(safeDeploymentTransaction.value),
    });

    console.log({ estGas }, estGas * (await client.getGasPrice()));

    // const hash = await signer.sendTransaction({
    //   to: safeDeploymentTransaction.to as `0x${string}`,
    //   data: safeDeploymentTransaction.data as Hex,
    //   value: BigInt(safeDeploymentTransaction.value),
    //   account: signer.account,
    // });

    // const receipt = await waitForTransactionReceipt(client, { hash });

    // return receipt;
  }

  const handleSetupWallet = async () => {
    try {
      // onSetSigners(signers);
      // onSetThreshold(Number.parseInt(threshold));

      const predictedSafe: PredictedSafeProps = {
        safeAccountConfig: {
          owners: signers,
          threshold: Number.parseInt(threshold),
        },
      };

      const protocolKit = await Safe.init({
        provider: sepolia.rpcUrls.default.http[0],
        predictedSafe: predictedSafe,
        signer: process.env.EXPO_PUBLIC_WALLET_PK,
      });

      const isDeployed = await protocolKit.isSafeDeployed();

      await deploySafe(protocolKit);
      console.log({ isDeployed });
    } catch (error) {
      console.error(error);
    }
  };

  const isPending = isCreatingWallet || isSettingUpWallet || isFetchingLocation;

  return (
    <KeyboardAvoidingView behavior='padding' style={styles.fullFlex}>
      <SafeAreaView edges={['bottom']} style={styles.container}>
        <FlatList
          data={signers}
          renderItem={({ item, index }) => (
            <View style={styles.containerKey} key={item} pointerEvents='auto'>
              <View style={styles.boxKey} pointerEvents='auto'>
                <ThemedText type='tinyLight' style={styles.keyTitle}>
                  {`Signer Address ${index + 1}`}
                </ThemedText>

                <ThemedText>{item}</ThemedText>
              </View>

              <CustomButton type='secondary' onPress={() => handleCopy(item)}>
                <Icons.Copy size={20} color='#fff' />
              </CustomButton>
            </View>
          )}
          keyExtractor={(item) => item}
          contentContainerStyle={{ gap: 16 }}
          showsVerticalScrollIndicator={false}
        />

        <Spacer height={16} />

        <TextInput
          label='Threshold'
          editable={signers.length > 0}
          keyboardType='number-pad'
          value={threshold}
          onChangeText={handleChangeThreshold}
        />

        <Spacer height={16} />

        <CustomButton
          type='primary'
          onPress={signers.length === 0 ? handleGenMultisig : handleSetupWallet}
          disabled={isLoading || isPending || (Number(signers.length) > 0 && Number(threshold || 0) <= 0)}
        >
          {signers.length === 0 ? 'Generate multisig' : 'Setup wallet'}
        </CustomButton>
      </SafeAreaView>
    </KeyboardAvoidingView>
  );
};

const useStyles = () => {
  const white05 = useTheme('white05');
  const white15 = useTheme('white15');
  const white35 = useTheme('white35');

  const styles = StyleSheet.create({
    containerKey: {
      backgroundColor: white05,
      borderWidth: 1,
      borderColor: white15,
      borderRadius: 16,
      padding: 16,
      gap: 16,
      flexDirection: 'row',
      alignItems: 'center',
    },
    boxKey: {
      flex: 1,
      flexDirection: 'column',
      gap: 2,
    },
    keyTitle: {
      color: white35,
    },
    container: {
      flex: 1,
      padding: 16,
    },
    actions: {
      flexDirection: 'column',
      gap: 8,
    },
    fullFlex: {
      flex: 1,
    },
  });

  return { styles };
};
