import { useMutation } from '@tanstack/react-query';
import { LocationObject } from 'expo-location';
import { genEOAWallet, geoHash, mnemonicToPK } from '@/utils/genWallet';

type Payload = {
  recoveryKey: string;
  location: LocationObject;
};

export const useCreateWallet = () => {
  return useMutation({
    mutationFn: async ({ recoveryKey, location }: Payload) => {
      const locationHash = geoHash(location?.coords.latitude, location?.coords.longitude, 7);

      const wallet = await genEOAWallet({
        locationHash,
        recoveryKey,
        iterations: 100_000,
      });

      if (!wallet?.seedPhrase) return null;

      const privateKeyHex = mnemonicToPK(wallet.seedPhrase);

      return {
        privateKey: privateKeyHex,
        seedPhrase: wallet?.seedPhrase,
      };
    },
  });
};
