import Safe, { PredictedSafeProps } from '@safe-global/protocol-kit';
import { useQuery } from '@tanstack/react-query';
import { Address } from 'viem';
import { sepolia } from 'viem/chains';
import { queryKeys } from '@/utils/queryKeys';

type Payload = {
  signers?: Address[];
  threshold?: number;
  safeAddress?: Address | null;
};

export const useSafeProtocolKit = () => {
  return useQuery({
    queryKey: queryKeys.safeProtocolKit,
    queryFn: async ({ signers, threshold, safeAddress }: Payload) => {
      if (safeAddress) {
        return await Safe.init({
          provider: sepolia.rpcUrls.default.http[0],
          safeAddress,
          signer: process.env.EXPO_PUBLIC_WALLET_PK,
        });
      }

      if (signers && threshold && Number(signers?.length || 0) > 0 && Number(threshold) > 0) {
        const predictedSafe: PredictedSafeProps = {
          safeAccountConfig: {
            owners: signers,
            threshold: Number.parseInt(threshold.toString()),
          },
        };

        return await Safe.init({
          provider: sepolia.rpcUrls.default.http[0],
          predictedSafe,
          signer: process.env.EXPO_PUBLIC_WALLET_PK,
        });
      }

      return null;
    },
  });
};
